12# -*- encoding: utf-8 -*-
import os
import logging
import json
from pathlib import Path
from afts import Afts
from threading import Thread
import time
# 统一继承MayaBaseHandler，可以自动获取组件面板的模型文件
from aistudio_serving.hanlder.pymps_handler import MayaBaseHandler
# 组件使用文档，详见 https://yuque.antfin-inc.com/aii/aistudio/nkyse5
# python lib 依赖写入 requirement.txt 
from PIL import Image
import requests
from io import BytesIO
from io_utils import load_image, get_url_from_afts

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        # logging.FileHandler("cover_enhance.log")
    ]
)
logger = logging.getLogger("CoverEnhance")

def get_subject_analysis(data):
    url = "https://paiplusinferencepre.alipay.com/inference/ccb90aaa457e5bb2_main_recognition_vlm/v1"
    body = {"features":{},"tensorFeatures":{"input_args":{"shapes":[1],"stringValues":["{\"cover_url\":\"https://mass.alipay.com/afts/file/MVYhSqfz1G4AAAAAgDAAAAgAennEAQBr?bizType=video_generation&token=trVYq6JrCGMCnthd13sCiZSF64QOUHc4ZfTYggxNhN0DAAAAZAAAxHloqGiC\",\"aiEffectList\":[{\"aftId\":\"dVWDTIVZ9tEAAAAAfUAAAAgAoBT3AABr\",\"effectType\":\"ai_stylize_GhibliStyle\",\"pageNo\":9,\"uuid\":\"1D9D67C5-53E3-4D96-BAFC-84001A9D2573\"}],\"generateId\":\"AIF2025080610523015618702814908\",\"componentName\":\"MayaCallbackComponent\"}"]}}}
    headers = {
        "Content-Type": "application/json;charset=utf-8",
        "MPS-app-name": "your-app-name",
        "MPS-http-version": "1.0",
        "MPS-trace-id": "your-trace-id"
    }

    json_data = json.loads(body['tensorFeatures']['input_args']['stringValues'][0])
    json_data['cover_url'] = data['cover_url']
    body['tensorFeatures']['input_args']['stringValues'][0] = json.dumps(json_data)
    response = requests.post(url=url, json=body, headers=headers).json()
    return response['resultMap']['output_args']

def enhance_image_quality(cover_url, cover_id):
    """
    调用画质增强模块
    """
    url = "https://paiplusinferencepre.alipay.com/inference/c679eb929e2cdd91_main_deeplpf/v1"
    body = {
        "features": {},
        "tensorFeatures": {
            "input": {
                "shapes": [1],
                "stringValues": [json.dumps({
                    "cover_url": cover_url,
                    "cover_id": cover_id
                })]
            }
        }
    }
    headers = {
        "Content-Type": "application/json;charset=utf-8",
        "MPS-app-name": "your-app-name",
        "MPS-http-version": "1.0",
        "MPS-trace-id": "your-trace-id"
    }

    try:
        response = requests.post(url=url, json=body, headers=headers)
        res = response.json()
        logger.info(f"Image quality enhancement response: {res}")

        if 'output' in res:
            output = res['output']
            if isinstance(output, str):
                output = json.loads(output)
            return output.get('cover_id'), output.get('cover_url')
        else:
            logger.warning("No output in enhancement response")
            return None, None

    except Exception as e:
        logger.error(f"Error in image quality enhancement: {e}")
        return None, None

# 用户自定义代码处理类
class UserHandler(MayaBaseHandler):
# class UserHandler:
    """
     model_dir: model.py 文件所在目录
    """
    def __init__(self, model_dir):
        # 父类初始化
        super(UserHandler, self).__init__(model_dir)
        resource_path = Path(self.resource_path)
        import sys
        sys.path.append(str(resource_path))

    def predict_np(self, features, trace_id):
        resultCode = 0       # 0表示成功，其它为失败
        errorMessage = "ok"  # errorMessage为predict函数对外透出的信息
        result_map = {
            "output": json.dumps({}, ensure_ascii=False)
        }

        start = time.time()
        logger.info("trace_id: " + str(trace_id) + ", Input Features:\n" + str(features))    

        try:
            data = json.loads(features.get("data").decode())
            content_id = data['contentId']
            logger.info(f"{content_id}: input: {features}")
            logger.info(f"content id: {content_id}\t标题: {data['title']}")

            # img = load_image(data['cover_id'])
            # img.save('tests/cover.png')
            if data['cover_url'] == '':
                data['cover_url'] = get_url_from_afts(data['cover_id'])

            # 获取分析结果
            subject_analysis = get_subject_analysis(data)
            subject_analysis = json.loads(subject_analysis)
            logger.info(f"subject analysis: {subject_analysis}")

            result = {
                'new_cover_id': data['cover_id'],
                'enhanced': False,
                'enhance_list': [],
            }

            if 'main_recognition' in subject_analysis and subject_analysis['main_recognition'] == 'food':
                logger.info("main recognition: food, calling image quality enhancement")

                # 调用画质增强模块
                enhanced_cover_id, enhanced_cover_url = enhance_image_quality(data['cover_url'], data['cover_id'])

                if enhanced_cover_id and enhanced_cover_url:
                    result = {
                        'new_cover_id': enhanced_cover_id,
                        'new_cover_url': enhanced_cover_url,
                        'enhanced': True,
                        'enhance_list': ['food'],
                    }
                    logger.info(f"Image quality enhancement successful: {enhanced_cover_id}")
                else:
                    logger.warning("Image quality enhancement failed, using original image")
                    result = {
                        'new_cover_id': data['cover_id'],
                        'enhanced': False,
                        'enhance_list': [],
                    }

            result_map = {
                "output": json.dumps(result, ensure_ascii=False)
            }

        except Exception as e: 
            resultCode=1
            errorMessage= "wrong_input"
            logger.info("trace_id: " + str(trace_id) + f", {e}")
            result_map = {
                "output": json.dumps({}, ensure_ascii=False)
            }

        logger.info(f"trace_id: {trace_id}\ttime: {time.time() - start}")

        return (resultCode, errorMessage, result_map)


def run_demo():
    # 使用绝对路径初始化
    model = UserHandler(os.getcwd())
    data_example = {
        'cover_id': 'A*zbI-S4hAlU8AAAAAevAAAAgAerZ9AQ', # afts id
        'cover_url': '',
        'title': '芋泥的受众群体到底是谁',
        'cat': '美食',
        'video_name': '',
        'video_id': '',
        'contentId': '20250724OB020010033349094691',
        'logs': {},
        'version': 1, 
    }
    print(json.dumps(data_example, ensure_ascii=False))
    res = model.predict_np({
            'data': json.dumps(data_example).encode()
        }, "test_trace_id")[-1]
    print(res)


# 用于调试UserHandler类的功能
if __name__ == "__main__":
    run_demo()